# Frontend-Backend Integration Instructions

This document provides step-by-step instructions to integrate the FastAPI backend with the Next.js frontend.

## Overview

The FastAPI backend has been modified to be 100% compatible with the frontend's expectations:

- **Registration**: Accepts `firstName`, `lastName`, `email`, `password`, `confirmPassword`
- **Login**: Accepts `email` and `password`
- **User Response**: Returns `{id: string, email: string, name: string, createdAt: string}`
- **Authentication**: Uses JWT tokens with proper validation
- **Password Requirements**: Enforces frontend's password strength rules

## Integration Steps

### 1. Backend Setup (Already Complete)

The backend is already configured and running. Key changes made:

- ✅ Updated User model to use `email` and `name` instead of `username`
- ✅ Modified registration to accept `firstName`/`lastName` and combine into `name`
- ✅ Updated login to use `email` instead of `username`
- ✅ Implemented password strength validation matching frontend requirements
- ✅ Updated all responses to match frontend's expected User object format

### 2. Frontend Integration

#### Step 2.1: Update Environment Configuration

Copy the `.env.local` file to your frontend root directory:

```bash
cp frontend-integration/.env.local frontend/.env.local
```

#### Step 2.2: Replace Auth Service

Replace the dummy auth service with the real implementation:

```bash
cp frontend-integration/auth-service-integration.ts frontend/src/lib/api/auth.ts
```

#### Step 2.3: Start Both Applications

1. **Start the Backend** (if not already running):
   ```bash
   cd registration_backend
   source venv/bin/activate
   python run.py
   ```
   Backend will be available at: http://localhost:8000

2. **Start the Frontend**:
   ```bash
   cd frontend
   npm install  # if not already done
   npm run dev
   ```
   Frontend will be available at: http://localhost:3000

### 3. Testing the Integration

#### 3.1: Test Registration Flow

1. Open http://localhost:3000/register
2. Fill in the registration form:
   - **Email**: <EMAIL>
   - **First Name**: Test
   - **Last Name**: User
   - **Password**: TestPass123! (must meet requirements)
   - **Confirm Password**: TestPass123!
3. Click Submit
4. Should redirect to dashboard with user logged in

#### 3.2: Test Login Flow

1. Open http://localhost:3000/login
2. Use the credentials from registration:
   - **Email**: <EMAIL>
   - **Password**: TestPass123!
3. Click Login
4. Should redirect to dashboard with user logged in

#### 3.3: Test Authentication Persistence

1. After logging in, refresh the page
2. User should remain logged in
3. Navigate between pages - authentication should persist

#### 3.4: Test Logout

1. From the dashboard, trigger logout
2. Should redirect to login page
3. Try accessing protected routes - should redirect to login

## API Endpoints

The backend provides these endpoints that the frontend now uses:

### POST /auth/register
**Request:**
```json
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

**Response:**
```json
{
  "id": "1",
  "email": "<EMAIL>",
  "name": "John Doe",
  "createdAt": "2024-01-01T12:00:00"
}
```

### POST /auth/login
**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### GET /auth/me
**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "id": "1",
  "email": "<EMAIL>", 
  "name": "John Doe",
  "createdAt": "2024-01-01T12:00:00"
}
```

## Password Requirements

Both frontend and backend enforce the same password requirements:
- Minimum 8 characters
- At least one uppercase letter
- At least one number  
- At least one special character

## Error Handling

The integration handles these error scenarios:
- Invalid email format
- Password strength validation
- Password confirmation mismatch
- Email already registered
- Invalid login credentials
- Expired or invalid tokens
- Network connectivity issues

## Security Features

- JWT token-based authentication
- Automatic token storage and management
- Token expiration handling
- Secure password hashing (bcrypt)
- Input validation on both frontend and backend
- CORS configuration for development

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Make sure backend CORS is configured for frontend origin
2. **Token Errors**: Check that tokens are being stored and sent correctly
3. **Validation Errors**: Ensure password meets all requirements
4. **Network Errors**: Verify both frontend and backend are running on correct ports

### Debug Steps:

1. Check browser console for JavaScript errors
2. Check browser Network tab for API call details
3. Check backend logs for server errors
4. Verify environment variables are loaded correctly

## Production Considerations

For production deployment:

1. **Environment Variables**: Update API_URL to production backend URL
2. **HTTPS**: Use HTTPS for both frontend and backend
3. **CORS**: Configure CORS for production domains only
4. **Secrets**: Use secure JWT secret keys
5. **Database**: Use production database (PostgreSQL/MySQL)
6. **Monitoring**: Add logging and error monitoring

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all integration steps were followed correctly
3. Check that both applications are running on the correct ports
4. Review browser console and network tabs for errors
