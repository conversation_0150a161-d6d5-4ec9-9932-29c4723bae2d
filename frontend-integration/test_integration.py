#!/usr/bin/env python3
"""
Test script to verify the backend integration works correctly.
Run this script to test all API endpoints before frontend integration.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_registration():
    """Test user registration endpoint."""
    print("🧪 Testing user registration...")
    
    # Test successful registration
    data = {
        "firstName": "Test",
        "lastName": "User",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirmPassword": "TestPass123!"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    
    if response.status_code == 201:
        user_data = response.json()
        print(f"✅ Registration successful: {user_data}")
        return user_data
    else:
        print(f"❌ Registration failed: {response.status_code} - {response.text}")
        return None

def test_login(email, password):
    """Test user login endpoint."""
    print("🧪 Testing user login...")
    
    data = {
        "email": email,
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    
    if response.status_code == 200:
        token_data = response.json()
        print(f"✅ Login successful: Token received")
        return token_data["access_token"]
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def test_get_current_user(token):
    """Test get current user endpoint."""
    print("🧪 Testing get current user...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    
    if response.status_code == 200:
        user_data = response.json()
        print(f"✅ Get current user successful: {user_data}")
        return user_data
    else:
        print(f"❌ Get current user failed: {response.status_code} - {response.text}")
        return None

def test_validation_errors():
    """Test various validation scenarios."""
    print("🧪 Testing validation errors...")
    
    # Test weak password
    data = {
        "firstName": "Test",
        "lastName": "User",
        "email": "<EMAIL>",
        "password": "weak",
        "confirmPassword": "weak"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    if response.status_code == 422:
        print("✅ Weak password validation working")
    else:
        print(f"❌ Weak password validation failed: {response.status_code}")
    
    # Test password mismatch
    data = {
        "firstName": "Test",
        "lastName": "User", 
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirmPassword": "DifferentPass123!"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    if response.status_code == 400 and "do not match" in response.text:
        print("✅ Password mismatch validation working")
    else:
        print(f"❌ Password mismatch validation failed: {response.status_code}")

def test_duplicate_email():
    """Test duplicate email registration."""
    print("🧪 Testing duplicate email registration...")
    
    data = {
        "firstName": "Duplicate",
        "lastName": "User",
        "email": "<EMAIL>",  # Same as first registration
        "password": "TestPass123!",
        "confirmPassword": "TestPass123!"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=data)
    if response.status_code == 400 and "already registered" in response.text:
        print("✅ Duplicate email validation working")
    else:
        print(f"❌ Duplicate email validation failed: {response.status_code}")

def test_invalid_login():
    """Test invalid login credentials."""
    print("🧪 Testing invalid login...")
    
    data = {
        "email": "<EMAIL>",
        "password": "WrongPassword123!"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    if response.status_code == 401:
        print("✅ Invalid login validation working")
    else:
        print(f"❌ Invalid login validation failed: {response.status_code}")

def main():
    """Run all tests."""
    print("🚀 Starting backend integration tests...\n")
    
    try:
        # Test server is running
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend server is not running. Please start it first.")
            sys.exit(1)
        print("✅ Backend server is running\n")
        
        # Test registration
        user_data = test_registration()
        if not user_data:
            print("❌ Registration test failed. Stopping tests.")
            sys.exit(1)
        print()
        
        # Test login
        token = test_login("<EMAIL>", "TestPass123!")
        if not token:
            print("❌ Login test failed. Stopping tests.")
            sys.exit(1)
        print()
        
        # Test get current user
        current_user = test_get_current_user(token)
        if not current_user:
            print("❌ Get current user test failed. Stopping tests.")
            sys.exit(1)
        print()
        
        # Test validation errors
        test_validation_errors()
        print()
        
        # Test duplicate email
        test_duplicate_email()
        print()
        
        # Test invalid login
        test_invalid_login()
        print()
        
        print("🎉 All tests passed! Backend is ready for frontend integration.")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Please make sure it's running on http://localhost:8000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
